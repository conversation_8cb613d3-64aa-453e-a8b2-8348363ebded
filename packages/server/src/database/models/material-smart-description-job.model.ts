import { DataTypes, Model, Sequelize } from '@infra-node/sequelize'

export enum MaterialSmartDescriptionJobStaging {
  UNTRIGGERED = 'untriggered',
  PENDING = 'pending',
  SUCCESS = 'succeeded',
  FAILED = 'failed',
}

export interface MaterialSmartDescriptionJobAttributes {
  id: number
  materialId: number
  materialVersion: string
  materialPubId: number
  resultId?: number | null
  staging: MaterialSmartDescriptionJobStaging
  rawConversation?: string | null
  rawResult?: string | null
  failedReason?: string | null
  createTime: number
  state: number
}

export class MaterialSmartDescriptionJob
  extends Model<
    MaterialSmartDescriptionJobAttributes,
    MaterialSmartDescriptionJobAttributes
  >
  implements MaterialSmartDescriptionJobAttributes {
  public id!: number
  public materialId!: number
  public materialVersion!: string
  public materialPubId!: number
  public resultId?: number | null
  public staging!: MaterialSmartDescriptionJobStaging
  public rawConversation?: string | null
  public rawResult?: string | null
  public failedReason?: string | null
  public createTime!: number
  public state!: number

  /**
   * 初始化智能描述生成作业模型
   */
  static initModel(sequelize: Sequelize): typeof MaterialSmartDescriptionJob {
    MaterialSmartDescriptionJob.init(
      {
        id: {
          type: DataTypes.NUMBER,
          primaryKey: true,
          allowNull: false,
          comment: '唯一标识',
        },
        materialId: {
          type: DataTypes.NUMBER,
          allowNull: false,
          field: 'material_id',
          comment: '物料标识',
        },
        materialVersion: {
          type: DataTypes.STRING(50),
          allowNull: false,
          field: 'material_version',
          comment: '物料版本号',
        },
        materialPubId: {
          type: DataTypes.NUMBER,
          allowNull: false,
          field: 'material_pub_id',
          comment: '冗余字段，直接指向指定物料版本记录',
        },
        resultId: {
          type: DataTypes.NUMBER,
          allowNull: true,
          field: 'result_id',
          comment: '指向结果表',
        },
        staging: {
          type: DataTypes.STRING(50),
          allowNull: false,
          validate: {
            isIn: [Object.values(MaterialSmartDescriptionJobStaging)],
          },
          comment: '运行情况: pending | failed | succeeded',
        },
        rawConversation: {
          type: DataTypes.STRING(256),
          allowNull: true,
          field: 'raw_conversation',
          comment: 'CDN 资源地址，与 agent 对话记录',
        },
        rawResult: {
          type: DataTypes.STRING(256),
          allowNull: true,
          field: 'raw_result',
          comment: 'CDN 资源地址，agent 原始生成结果',
        },
        failedReason: {
          type: DataTypes.STRING(256),
          allowNull: true,
          field: 'failed_reason',
          comment: '失败原因',
        },
        createTime: {
          type: DataTypes.NUMBER,
          allowNull: false,
          field: 'create_time',
          comment: '创建时间 (JS 时间戳)',
        },
        state: {
          type: DataTypes.INTEGER,
          allowNull: false,
          defaultValue: 1,
          comment: '生效状态: -1 删除，0 待上线，1 正常',
        },
      },
      {
        sequelize,
        modelName: 'MaterialSmartDescriptionJob',
        tableName: 'material_smart_description_job',
        timestamps: false, // 使用自定义的 create_time 字段
        underscored: false, // 已经手动映射字段名
        comment: '智能描述生成过程表',
        indexes: [
          {
            name: 'idx_material_id',
            fields: ['material_id'],
          },
          {
            name: 'idx_material_version',
            fields: ['material_version'],
          },
          {
            name: 'idx_material_pub_id',
            fields: ['material_pub_id'],
          },
          {
            name: 'idx_staging',
            fields: ['staging'],
          },
          {
            name: 'idx_create_time',
            fields: ['create_time'],
          },
        ],
      },
    )

    return MaterialSmartDescriptionJob
  }

  /**
   * 检查作业是否完成
   */
  isCompleted(): boolean {
    return this.staging === 'succeeded' || this.staging === 'failed'
  }

  /**
   * 检查作业是否成功
   */
  isSucceeded(): boolean {
    return this.staging === 'succeeded'
  }

  /**
   * 检查作业是否失败
   */
  isFailed(): boolean {
    return this.staging === 'failed'
  }

  /**
   * 检查作业是否正在进行中
   */
  isPending(): boolean {
    return this.staging === 'pending'
  }

  /**
   * 检查记录是否有效（未删除）
   */
  isValid(): boolean {
    return this.state >= 0
  }

  /**
   * 检查记录是否已删除
   */
  isDeleted(): boolean {
    return this.state === -1
  }
}

import { JsonObject } from 'type-fest'
import { Injectable, Logger } from '@nestjs/common'
import { get, set, cloneDeep } from 'lodash'

import { DatabaseService } from '../../database/database.service'
import { MaterialSmartDescription } from '../../database/models'
import { ModelManager } from '../../database/models'

@Injectable()
export class MaterialSmartDescriptionService {
  private readonly logger = new Logger(MaterialSmartDescriptionService.name)

  constructor(private readonly databaseService: DatabaseService) {}

  async createSmartDescription(params: {
    jobId: number
    materialId: number
    materialPubId: number
    materialVersion: string
    result: Service.MaterialSmartDescription.BasicMaterialDescription
    namespace: string
    schemaUrl?: string | null
    publishStatus?: number
  }): Promise<MaterialSmartDescription> {
    const now = Date.now()
    const uuid = ModelManager.genPrimaryIndex()
    const models = this.databaseService.getModels()
    const description = await models.MaterialSmartDescription.create({
      id: uuid,
      materialId: params.materialId,
      materialPubId: params.materialPubId,
      materialVersion: params.materialVersion,
      jobId: params.jobId,
      smartDescription: params.result as unknown as JsonObject,
      createTime: now,
      state: 1,
      namespace: params.namespace,
      schemaUrl: params.schemaUrl || null,
      publishStatus: params.publishStatus || 0, // 默认为草稿状态
    })
    return description.toJSON() as unknown as MaterialSmartDescription
  }

  /**
   * 根据 ID 查找描述
   */
  async findDescriptionById(
    id: number,
  ): Promise<MaterialSmartDescription | null> {
    try {
      const models = this.databaseService.getModels()
      const description = await models.MaterialSmartDescription.findByPk(id)
      return description?.toJSON() as MaterialSmartDescription
    }
    catch (error) {
      this.logger.error(`查找描述失败 ID: ${id}`, error)
      throw error
    }
  }

  /**
   * 根据物料 ID 查找描述
   */
  async findDescriptionsByMaterialId(
    materialId: number,
    limit: number = 10,
    offset: number = 0,
  ): Promise<MaterialSmartDescription[]> {
    try {
      const models = this.databaseService.getModels()
      const descriptions = await models.MaterialSmartDescription.findAll({
        where: {
          materialId,
          state: 1, // 只查询正常状态的记录
        },
        limit: Number(limit),
        offset: Number(offset),
        order: [['createTime', 'DESC']],
      })
      return descriptions.map(
        desc => desc.toJSON() as MaterialSmartDescription,
      )
    }
    catch (error) {
      this.logger.error(
        `根据物料ID查找描述失败 materialId: ${materialId}`,
        error,
      )
      throw error
    }
  }

  /**
   * 根据物料版本查找描述
   */
  async findDescriptionsByMaterialVersion(
    materialVersion: string,
    limit: number = 10,
    offset: number = 0,
  ): Promise<MaterialSmartDescription[]> {
    try {
      const models = this.databaseService.getModels()
      const descriptions = await models.MaterialSmartDescription.findAll({
        where: {
          materialVersion,
          state: 1, // 只查询正常状态的记录
        },
        limit: Number(limit),
        offset: Number(offset),
        order: [['createTime', 'DESC']],
      })
      return descriptions.map(
        desc => desc.toJSON() as MaterialSmartDescription,
      )
    }
    catch (error) {
      this.logger.error(
        `根据物料版本查找描述失败 materialVersion: ${materialVersion}`,
        error,
      )
      throw error
    }
  }

  /**
   * 根据作业 ID 查找描述
   */
  async findDescriptionByJobId(
    jobId: number,
  ): Promise<MaterialSmartDescription | null> {
    try {
      const models = this.databaseService.getModels()
      const description = await models.MaterialSmartDescription.findOne({
        where: {
          jobId,
          state: 1, // 只查询正常状态的记录
        },
      })
      return description?.toJSON() as MaterialSmartDescription
    }
    catch (error) {
      this.logger.error(`根据作业ID查找描述失败 jobId: ${jobId}`, error)
      throw error
    }
  }

  /**
   * 获取所有描述（支持分页和过滤）
   */
  async findAllDescriptions(
    limit: number = 10,
    offset: number = 0,
  ): Promise<MaterialSmartDescription[]> {
    try {
      const models = this.databaseService.getModels()
      const descriptions = await models.MaterialSmartDescription.findAll({
        where: {
          state: 1, // 只查询正常状态的记录
        },
        limit,
        offset,
        order: [['createTime', 'DESC']],
      })
      return descriptions.map(
        desc => desc.toJSON() as MaterialSmartDescription,
      )
    }
    catch (error) {
      this.logger.error('获取描述列表失败', error)
      throw error
    }
  }

  /**
   * 更新智能描述内容
   */
  async updateDescriptionContent(
    id: number,
    smartDescription: Service.MaterialSmartDescription.BasicMaterialDescription,
  ): Promise<MaterialSmartDescription | null> {
    try {
      const models = this.databaseService.getModels()
      const description = await models.MaterialSmartDescription.findByPk(id)

      if (!description) {
        return null
      }

      await description.update({
        smartDescription: smartDescription as unknown as JsonObject,
      })
      this.logger.log(`智能描述内容更新成功: ${description.id}`)
      return description.toJSON() as MaterialSmartDescription
    }
    catch (error) {
      this.logger.error(`更新智能描述内容失败 ID: ${id}`, error)
      throw error
    }
  }

  /**
   * 更新描述状态
   */
  async updateDescriptionState(
    id: number,
    state: number,
  ): Promise<MaterialSmartDescription | null> {
    try {
      const models = this.databaseService.getModels()
      const description = await models.MaterialSmartDescription.findByPk(id)

      if (!description) {
        return null
      }

      await description.update({ state })
      this.logger.log(`描述状态更新成功: ${description.id}, 新状态: ${state}`)
      return description.toJSON() as MaterialSmartDescription
    }
    catch (error) {
      this.logger.error(`更新描述状态失败 ID: ${id}`, error)
      throw error
    }
  }

  /**
   * 根据物料 ID 和版本查找最新的有效描述
   */
  async findLatestDescriptionByMaterial(
    materialId: number,
    materialVersion?: string,
  ): Promise<MaterialSmartDescription | null> {
    try {
      const models = this.databaseService.getModels()
      const whereCondition: Record<string, unknown> = {
        materialId,
        state: 1, // 只查询正常状态的记录
      }

      if (materialVersion) {
        whereCondition.materialVersion = materialVersion
      }

      const description = await models.MaterialSmartDescription.findOne({
        where: whereCondition,
        order: [['createTime', 'DESC']],
      })

      return description?.toJSON() as MaterialSmartDescription
    }
    catch (error) {
      this.logger.error(`查找最新描述失败 materialId: ${materialId}`, error)
      throw error
    }
  }

  /**
   * 根据物料 ID 查找待处理描述
   */
  async findPendingDescriptionsByMaterialId(
    materialId: number,
    limit: number = 10,
    offset: number = 0,
  ): Promise<MaterialSmartDescription[]> {
    try {
      const models = this.databaseService.getModels()
      const descriptions = await models.MaterialSmartDescription.findAll({
        where: {
          materialId,
          state: 0, // 只查询待处理状态的记录
        },
        limit,
        offset,
        order: [['createTime', 'DESC']],
      })
      return descriptions.map(
        desc => desc.toJSON() as MaterialSmartDescription,
      )
    }
    catch (error) {
      this.logger.error(
        `根据物料ID查找待处理描述失败 materialId: ${materialId}`,
        error,
      )
      throw error
    }
  }

  /**
   * 执行原生 SQL 查询
   */
  async executeRawQuery(sql: string): Promise<unknown> {
    try {
      const result = await this.databaseService.query(sql)
      this.logger.log('执行原生 SQL 查询成功')
      return result
    }
    catch (error) {
      this.logger.error('执行原生 SQL 查询失败', error)
      throw error
    }
  }

  /**
   * 更新 ZhiDa 描述内容
   * 通过创建新记录的方式实现更新，保留历史版本
   */
  async updateZhidaDescription(
    params: Service.MaterialSmartDescription.UpdateZhidaDescriptionParams,
  ): Promise<Service.MaterialSmartDescription.UpdateZhidaDescriptionResult> {
    try {
      const { id, updateDto } = params

      // 查找原始记录
      const originalDescription = await this.findDescriptionById(id)
      if (!originalDescription) {
        return {
          success: false,
          error: '描述不存在',
        }
      }

      // 深拷贝现有的 smartDescription 以避免修改原始数据
      const updatedSmartDescription = cloneDeep(
        originalDescription.smartDescription,
      ) as unknown as Service.MaterialSmartDescription.BasicMaterialDescription

      // 更新对应的字段
      if (updateDto.description !== undefined) {
        set(
          updatedSmartDescription,
          'exportType.explanation',
          updateDto.description,
        )
      }

      if (updateDto.propsDefine !== undefined) {
        set(
          updatedSmartDescription,
          'api.parameters.typescriptCode',
          updateDto.propsDefine,
        )
      }

      if (updateDto.jsxDemo !== undefined) {
        set(updatedSmartDescription, 'usage.basicExample', updateDto.jsxDemo)
      }

      // 更新 payload 中的字段
      const payload = get(
        updatedSmartDescription,
        'payload',
        {},
      ) as Partial<Service.MaterialSmartDescription.MaterialUserDefine>

      if (updateDto.childNested !== undefined) {
        payload.childNested = updateDto.childNested
      }

      if (updateDto.jsxPropCompatible !== undefined) {
        payload.jsxPropCompatible = updateDto.jsxPropCompatible
      }

      if (updateDto.mergePropsBeforeInsert !== undefined) {
        payload.mergePropsBeforeInsert = updateDto.mergePropsBeforeInsert
      }

      if (updateDto.purePropEffect !== undefined) {
        payload.purePropEffect = updateDto.purePropEffect
      }

      // 将更新后的 payload 设置回 smartDescription
      set(updatedSmartDescription, 'payload', payload)

      // 创建新记录而不是更新现有记录
      const newDescription = await this.createSmartDescriptionFromExisting({
        originalDescription,
        updatedSmartDescription,
      })

      if (!newDescription) {
        return {
          success: false,
          error: '创建新记录失败',
        }
      }

      return {
        success: true,
        data: newDescription as unknown as Service.MaterialSmartDescription.EnrichedDescription,
      }
    }
    catch (error) {
      this.logger.error(`更新 ZhiDa 描述失败 ID: ${params.id}`, error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误',
      }
    }
  }

  /**
   * 基于现有记录创建新的智能描述记录
   * 保留原有记录的基本信息，但使用新的 smartDescription 和 createTime
   */
  async createSmartDescriptionFromExisting(params: {
    originalDescription: MaterialSmartDescription
    updatedSmartDescription: Service.MaterialSmartDescription.BasicMaterialDescription
  }): Promise<MaterialSmartDescription | null> {
    try {
      const { originalDescription, updatedSmartDescription } = params
      const now = Date.now()
      const uuid = ModelManager.genPrimaryIndex()
      const models = this.databaseService.getModels()

      const newDescription = await models.MaterialSmartDescription.create({
        id: uuid,
        materialId: originalDescription.materialId,
        materialPubId: originalDescription.materialPubId,
        materialVersion: originalDescription.materialVersion,
        jobId: originalDescription.jobId, // 保持原有的 jobId
        smartDescription: updatedSmartDescription as unknown as JsonObject,
        createTime: now, // 使用当前时间作为新的创建时间
        state: 1, // 新记录状态为正常
        namespace: originalDescription.namespace, // 保持原有的 namespace
        schemaUrl: originalDescription.schemaUrl, // 保持原有的 schemaUrl
        publishStatus: originalDescription.publishStatus, // 保持原有的发布状态
      })

      this.logger.log(
        `基于记录 ${originalDescription.id} 创建新的智能描述记录: ${newDescription.id}`,
      )
      return newDescription.toJSON() as unknown as MaterialSmartDescription
    }
    catch (error) {
      this.logger.error(`创建新智能描述记录失败`, error)
      throw error
    }
  }

  /**
   * 根据物料标识参数查找最新的有效描述
   */
  async findLatestDescriptionByMaterialIdentifier(
    params: Service.Forward.MaterialPlatform.MaterialIdentifierParams,
  ): Promise<MaterialSmartDescription | null> {
    try {
      const models = this.databaseService.getModels()
      const whereCondition: Record<string, unknown> = {
        state: 1, // 只查询正常状态的记录
      }

      // 根据传入的参数构建查询条件
      if (params.materialId) {
        whereCondition.materialId = params.materialId
      }

      if (params.materialVersionName) {
        whereCondition.materialVersion = params.materialVersionName
      }

      // 如果提供了 namespace，可以直接通过 namespace 查询
      if (params.namespace) {
        whereCondition.namespace = params.namespace
      }

      const description = await models.MaterialSmartDescription.findOne({
        where: whereCondition,
        order: [['createTime', 'DESC']],
      })

      return description?.toJSON() as MaterialSmartDescription
    }
    catch (error) {
      this.logger.error('根据物料标识参数查找最新描述失败', error)
      throw error
    }
  }

  /**
   * 根据 namespace 查找描述
   */
  async findDescriptionsByNamespace(
    namespace: string,
    limit: number = 10,
    offset: number = 0,
  ): Promise<MaterialSmartDescription[]> {
    try {
      const models = this.databaseService.getModels()
      const descriptions = await models.MaterialSmartDescription.findAll({
        where: {
          namespace,
          state: 1, // 只查询正常状态的记录
        },
        limit: Number(limit),
        offset: Number(offset),
        order: [['createTime', 'DESC']],
      })
      return descriptions.map(
        desc => desc.toJSON() as MaterialSmartDescription,
      )
    }
    catch (error) {
      this.logger.error(
        `根据 namespace 查找描述失败 namespace: ${namespace}`,
        error,
      )
      throw error
    }
  }

  /**
   * 根据发布状态查找描述
   */
  async findDescriptionsByPublishStatus(
    publishStatus: number,
    limit: number = 10,
    offset: number = 0,
  ): Promise<MaterialSmartDescription[]> {
    try {
      const models = this.databaseService.getModels()
      const descriptions = await models.MaterialSmartDescription.findAll({
        where: {
          publishStatus,
          state: 1, // 只查询正常状态的记录
        },
        limit: Number(limit),
        offset: Number(offset),
        order: [['createTime', 'DESC']],
      })
      return descriptions.map(
        desc => desc.toJSON() as MaterialSmartDescription,
      )
    }
    catch (error) {
      this.logger.error(
        `根据发布状态查找描述失败 publishStatus: ${publishStatus}`,
        error,
      )
      throw error
    }
  }

  /**
   * 更新发布状态
   */
  async updatePublishStatus(
    id: number,
    publishStatus: number,
  ): Promise<MaterialSmartDescription | null> {
    try {
      const models = this.databaseService.getModels()
      const description = await models.MaterialSmartDescription.findByPk(id)

      if (!description) {
        return null
      }

      await description.update({
        publishStatus,
      })
      this.logger.log(`发布状态更新成功: ${description.id} -> ${publishStatus}`)
      return description.toJSON() as MaterialSmartDescription
    }
    catch (error) {
      this.logger.error(`更新发布状态失败 ID: ${id}`, error)
      throw error
    }
  }

  /**
   * 构建 ZhiDa 描述对象
   * 将构建逻辑抽取为独立方法，便于复用
   */
  buildZhidaDescription(
    params: Service.MaterialSmartDescription.BuildZhidaDescriptionParams,
  ): Service.MaterialSmartDescription.ZhiDaNeededMaterialDescription {
    const { smartDescription, materialDetail } = params

    return {
      title: materialDetail.title,
      name: materialDetail.name,
      namespace: materialDetail.namespace,
      description: get(
        smartDescription,
        'exportType.explanation',
        '',
      ) as string,
      propsDefine: get(
        smartDescription,
        'api.parameters.typescriptCode',
        '',
      ) as string,
      jsxDemo: get(smartDescription, 'usage.basicExample', []) as string[],
      ...(get(
        smartDescription,
        'payload',
        {},
      ) as Partial<Service.MaterialSmartDescription.MaterialUserDefine>),
    }
  }
}

import { Controller, Get, Param, Query, Put, Body, Post } from '@nestjs/common'
import { MaterialSmartDescriptionService } from './material-smart-description.service'
import { ForwardMaterialPlatformService } from '../forward/material-platform/material-platform.service'
import { JsonValue } from 'type-fest'
import { MaterialSmartDescription } from '@/database/models'

@Controller('material-smart-description')
export class MaterialSmartDescriptionController {
  constructor(
    private readonly descriptionService: MaterialSmartDescriptionService,
    private readonly materialPlatformService: ForwardMaterialPlatformService,
  ) {}

  /**
   * 分页获取物料智能描述列表
   */
  @Get()
  async getDescriptions(
    @Query() query: Service.PaginationParams,
  ): Promise<
      Service.PaginationResult<Service.MaterialSmartDescription.EnrichedDescription>
    > {
    const { pageNum = 1, pageSize = 10 } = query
    const offset = (pageNum - 1) * pageSize

    // 使用兼容 MySQL 5.7 的查询方式，通过子查询获取每个 material_pub_id 的最新记录
    const sql = `
      SELECT d1.*
      FROM material_smart_description d1
      INNER JOIN (
        SELECT material_pub_id, MAX(create_time) as max_create_time
        FROM material_smart_description
        WHERE state = 1
        GROUP BY material_pub_id
      ) d2 ON d1.material_pub_id = d2.material_pub_id AND d1.create_time = d2.max_create_time
      WHERE d1.state = 1
      ORDER BY d1.create_time DESC
      LIMIT ${pageSize} OFFSET ${offset}
    `

    const countSql = `
      SELECT COUNT(DISTINCT material_pub_id) as total
      FROM material_smart_description
      WHERE state = 1
    `

    const [descriptions, countResult] = await Promise.all([
      this.descriptionService.executeRawQuery(sql),
      this.descriptionService.executeRawQuery(countSql),
    ])

    const total = (countResult as { total: number }[])[0]?.total || 0
    const totalPage = Math.ceil(total / pageSize)

    // 获取物料基础信息
    const enrichedDescriptions = await Promise.all(
      (
        descriptions as {
          id: number
          material_id: number
          material_version: string
          material_pub_id: number
          job_id: number
          smart_description: JsonValue
          create_time: number
          state: number
          namespace: string
          schema_url?: string | null
          publish_status: number
        }[]
      ).map(
        async (
          desc,
        ): Promise<Service.MaterialSmartDescription.EnrichedDescription> => {
          try {
            const materialDetail
              = await this.materialPlatformService.getMaterialVersionDetail({
                materialId: desc.material_id,
                materialVersionName: desc.material_version,
              })

            const materialDetailInfo: Service.MaterialSmartDescription.MaterialDetailInfo
              = {
                title: materialDetail.title,
                name: materialDetail.currentVersion.schema.componentName,
                namespace: materialDetail.namespace,
              }

            // 使用 service 层的构建方法
            const zhidaDescription
              = this.descriptionService.buildZhidaDescription({
                smartDescription: desc.smart_description,
                materialDetail: materialDetailInfo,
              })

            return {
              id: desc.id,
              materialId: desc.material_id,
              materialVersion: desc.material_version,
              materialPubId: desc.material_pub_id,
              jobId: desc.job_id,
              smartDescription: desc.smart_description,
              createTime: desc.create_time,
              state: desc.state,
              materialDetail: materialDetailInfo,
              zhidaDescription,
              namespace: desc.namespace,
              schemaUrl: desc.schema_url,
              publishStatus: desc.publish_status,
            }
          }
          catch (error) {
            console.error(`获取物料详情失败: ${desc.material_id}`, error)

            const fallbackMaterialDetail: Service.MaterialSmartDescription.MaterialDetailInfo
              = {
                title: '未知物料',
                name: 'unknown',
                namespace: '未知',
              }

            // 使用 service 层的构建方法，即使是错误情况
            const zhidaDescription
              = this.descriptionService.buildZhidaDescription({
                smartDescription: desc.smart_description,
                materialDetail: fallbackMaterialDetail,
              })

            return {
              id: desc.id,
              materialId: desc.material_id,
              materialVersion: desc.material_version,
              materialPubId: desc.material_pub_id,
              jobId: desc.job_id,
              smartDescription: desc.smart_description,
              createTime: desc.create_time,
              state: desc.state,
              materialDetail: fallbackMaterialDetail,
              zhidaDescription,
              namespace: desc.namespace,
              schemaUrl: desc.schema_url,
              publishStatus: desc.publish_status,
            }
          }
        },
      ),
    )

    return {
      list: enrichedDescriptions,
      total,
      pageNum,
      pageSize,
      totalPage,
      hasNext: pageNum < totalPage,
      hasPrev: pageNum > 1,
    }
  }

  /**
   * 更新 ZhiDa 描述内容
   * 只允许修改映射到 zhidaDescription 的字段，除了 name 和 namespace
   */
  @Put(':id/zhida-description')
  async updateZhidaDescription(
    @Param('id') id: string,
    @Body()
    updateDto: Service.MaterialSmartDescription.UpdateZhidaDescriptionDto,
  ): Promise<Service.MaterialSmartDescription.EnrichedDescription> {
    // 使用 service 层的方法处理更新逻辑
    const result = await this.descriptionService.updateZhidaDescription({
      id: Number(id),
      updateDto,
    })

    if (!result.success) {
      throw new Error(result.error || '更新失败')
    }

    // 获取物料基础信息并构建完整的响应
    const updatedDescription = result.data!
    let materialDetail: Service.MaterialSmartDescription.MaterialDetailInfo
    try {
      const detail
        = await this.materialPlatformService.getMaterialVersionDetail({
          materialId: updatedDescription.materialId,
          materialVersionName: updatedDescription.materialVersion,
        })
      materialDetail = {
        title: detail.title,
        name: detail.currentVersion.schema.componentName,
        namespace: detail.namespace,
      }
    }
    catch (error) {
      console.error(
        `获取物料详情失败: ${updatedDescription.materialId}`,
        error,
      )
      materialDetail = {
        title: '未知物料',
        name: 'unknown',
        namespace: '未知',
      }
    }

    // 构建 zhidaDescription
    const zhidaDescription = this.descriptionService.buildZhidaDescription({
      smartDescription: updatedDescription.smartDescription,
      materialDetail,
    })

    // 返回更新后的完整数据
    return {
      ...updatedDescription,
      materialDetail,
      zhidaDescription,
    }
  }

  /**
   * 根据 ID 获取单个描述详情
   */
  @Get(':id')
  async getDescriptionById(
    @Param('id') id: string,
  ): Promise<Service.MaterialSmartDescription.EnrichedDescription> {
    const description = await this.descriptionService.findDescriptionById(
      Number(id),
    )

    if (!description) {
      throw new Error('描述不存在')
    }

    // 获取物料基础信息
    let materialDetail: Service.MaterialSmartDescription.MaterialDetailInfo
    try {
      const detail
        = await this.materialPlatformService.getMaterialVersionDetail({
          materialId: description.materialId,
          materialVersionName: description.materialVersion,
        })
      materialDetail = {
        title: detail.title,
        name: detail.currentVersion.schema.componentName,
        namespace: detail.namespace,
      }
    }
    catch (error) {
      console.error(`获取物料详情失败: ${description.materialId}`, error)
      materialDetail = {
        title: '未知物料',
        name: 'unknown',
        namespace: '未知',
      }
    }

    // 使用 service 层的构建方法
    const zhidaDescription = this.descriptionService.buildZhidaDescription({
      smartDescription: description.smartDescription,
      materialDetail,
    })

    return {
      ...description,
      materialDetail,
      zhidaDescription,
    }
  }

  /**
   * 根据物料 ID 获取历史版本
   */
  @Get('material/:materialId/history')
  async getHistoryByMaterialId(
    @Param('materialId') materialId: string,
    @Query() query: Service.PaginationParams,
  ): Promise<Service.PaginationResult<MaterialSmartDescription>> {
    const { pageNum = 1, pageSize = 10 } = query
    const offset = (pageNum - 1) * pageSize

    const descriptions
      = await this.descriptionService.findDescriptionsByMaterialId(
        Number(materialId),
        pageSize,
        offset,
      )

    // 获取总数
    const allDescriptions
      = await this.descriptionService.findDescriptionsByMaterialId(
        Number(materialId),
        1000,
        0,
      )
    const total = allDescriptions.length
    const totalPage = Math.ceil(total / pageSize)

    return {
      list: descriptions,
      total,
      pageNum,
      pageSize,
      totalPage,
      hasNext: pageNum < totalPage,
      hasPrev: pageNum > 1,
    }
  }

  /**
   * 根据 namespace 获取描述列表
   */
  @Get('namespace/:namespace')
  async getDescriptionsByNamespace(
    @Param('namespace') namespace: string,
    @Query() query: Service.PaginationParams,
  ): Promise<Service.PaginationResult<MaterialSmartDescription>> {
    const { pageNum = 1, pageSize = 10 } = query
    const offset = (pageNum - 1) * pageSize

    const descriptions = await this.descriptionService.findDescriptionsByNamespace(
      namespace,
      pageSize,
      offset,
    )

    // 获取总数（简化实现，实际应该有专门的计数方法）
    const total = descriptions.length
    const totalPage = Math.ceil(total / pageSize)

    return {
      list: descriptions,
      total,
      pageNum,
      pageSize,
      totalPage,
      hasNext: pageNum < totalPage,
      hasPrev: pageNum > 1,
    }
  }

  /**
   * 根据发布状态获取描述列表
   */
  @Get('publish-status/:status')
  async getDescriptionsByPublishStatus(
    @Param('status') status: string,
    @Query() query: Service.PaginationParams,
  ): Promise<Service.PaginationResult<MaterialSmartDescription>> {
    const { pageNum = 1, pageSize = 10 } = query
    const offset = (pageNum - 1) * pageSize
    const publishStatus = Number(status)

    const descriptions = await this.descriptionService.findDescriptionsByPublishStatus(
      publishStatus,
      pageSize,
      offset,
    )

    // 获取总数（简化实现，实际应该有专门的计数方法）
    const total = descriptions.length
    const totalPage = Math.ceil(total / pageSize)

    return {
      list: descriptions,
      total,
      pageNum,
      pageSize,
      totalPage,
      hasNext: pageNum < totalPage,
      hasPrev: pageNum > 1,
    }
  }

  /**
   * 更新发布状态
   */
  @Put(':id/publish-status')
  async updatePublishStatus(
    @Param('id') id: string,
    @Body() body: { publishStatus: number },
  ): Promise<MaterialSmartDescription> {
    const result = await this.descriptionService.updatePublishStatus(
      Number(id),
      body.publishStatus,
    )

    if (!result) {
      throw new Error('描述不存在或更新失败')
    }

    return result
  }

  /**
   * 通过物料标识参数查询物料的 zhidaDescription
   */
  @Post('query')
  async getDescriptionByMaterialIdentifier(
    @Body() params: Service.Forward.MaterialPlatform.MaterialIdentifierParams,
  ): Promise<Service.MaterialSmartDescription.ZhiDaNeededMaterialDescription | null> {
    // 验证参数
    ForwardMaterialPlatformService.assertMaterialIdentifier(params)

    let materialId = params.materialId
    const materialVersionName = params.materialVersionName!

    // 如果只提供了 namespace，需要先通过物料平台获取物料详情
    if (params.namespace && !params.materialId) {
      try {
        const materialDetail
          = await this.materialPlatformService.getMaterialVersionDetail(params)
        materialId = materialDetail.id
      }
      catch (error) {
        console.error('通过 namespace 获取物料详情失败', error)
        return null
      }
    }

    if (!materialId) {
      throw new Error('无法确定物料 ID')
    }

    // 查找最新的描述记录
    const description
      = await this.descriptionService.findLatestDescriptionByMaterial(
        materialId,
        materialVersionName,
      )

    if (!description) {
      return null
    }

    // 获取物料基础信息
    let materialDetail: Service.MaterialSmartDescription.MaterialDetailInfo
    try {
      const detail
        = await this.materialPlatformService.getMaterialVersionDetail({
          materialId: description.materialId,
          materialVersionName: description.materialVersion,
        })
      materialDetail = {
        title: detail.title,
        name: detail.currentVersion.schema.componentName,
        namespace: detail.namespace,
      }
    }
    catch (error) {
      console.error(`获取物料详情失败: ${description.materialId}`, error)
      materialDetail = {
        title: '未知物料',
        name: 'unknown',
        namespace: '未知',
      }
    }

    // 构建 zhidaDescription
    const zhidaDescription = this.descriptionService.buildZhidaDescription({
      smartDescription: description.smartDescription,
      materialDetail,
    })

    return zhidaDescription
  }
}
